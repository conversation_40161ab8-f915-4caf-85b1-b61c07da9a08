{
  "name": "Dr.QP ROS 2 jazzy",
  "remoteUser": "rosdev",
  "image": "ghcr.io/dr-qp/jazzy-ros-desktop:edge",
  "customizations": {
    "vscode": {
      "extensions": [
        // Open the /home/<USER>/ros2_ws/Dr.QP.code-workspace to install the recommended extensions
      ]
    }
  },
  "workspaceFolder": "/home/<USER>/ros2_ws/",
  "workspaceMount": "source=${localWorkspaceFolder},target=/home/<USER>/ros2_ws/,type=bind",
  "containerEnv": {},
  "mounts": [
  ],
  "postCreateCommand": "sudo chown rosdev:rosdev /home/<USER>/ros2_ws/.ansible /home/<USER>/ros2_ws/.vscode /home/<USER>/ros2_ws/.cache /home/<USER>/ros2_ws/build /home/<USER>/ros2_ws/install /home/<USER>/ros2_ws/lcov /home/<USER>/ros2_ws/log /home/<USER>/ros2_ws/docs/_build /home/<USER>/ros2_ws/.micromamba /home/<USER>/ros2_ws/.venv && python3 -m venv /home/<USER>/ros2_ws/.venv && /home/<USER>/ros2_ws/.venv/bin/python3 -m pip install -r /home/<USER>/ros2_ws/requirements.txt",
  "runArgs": [
    // Commented out to allow Codespaces use
    // "--network", "host",
    // "--ipc", "host",
    // "--pid", "host",
    // "--device", "/dev/ttySC0"

    "--mount", "type=volume,source=drqp-ansible,target=/home/<USER>/ros2_ws/.ansible",
    "--mount", "type=volume,source=drqp-vscode,target=/home/<USER>/ros2_ws/.vscode",
    "--mount", "type=volume,source=drqp-clangd-cache,target=/home/<USER>/ros2_ws/.cache",
    "--mount", "type=volume,source=drqp-colcon-build,target=/home/<USER>/ros2_ws/build",
    "--mount", "type=volume,source=drqp-colcon-install,target=/home/<USER>/ros2_ws/install",
    "--mount", "type=volume,source=drqp-colcon-lcov,target=/home/<USER>/ros2_ws/lcov",
    "--mount", "type=volume,source=drqp-colcon-log,target=/home/<USER>/ros2_ws/log",
    "--mount", "type=volume,source=drqp-docs-build,target=/home/<USER>/ros2_ws/docs/_build",
    "--mount", "type=volume,source=drqp-python-micromamba,target=/home/<USER>/ros2_ws/.micromamba",
    "--mount", "type=volume,source=drqp-python-venv,target=/home/<USER>/ros2_ws/.venv",
  ],
  "features": {
  }
}
