name: CI/CD

on:
  push:
    branches:
      - main
    tags:
      - v*
  pull_request:
    branches:
      - main
  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

env:
  ROS_DISTRO: jazzy
  REGISTRY: ghcr.io
  REGISTRY_USERNAME: ${{ github.actor }} # for nektos/act, use your own username via --actor option

jobs:
  paths-filter:
    name: Need to run?
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions: {}
    outputs:
      pass: ${{ steps.filter.outputs.pass }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Filters
        uses: ./.github/actions/paths-filter
        id: filter
        with:
          extra-filter: .github/workflows/ci.yml

  build-dev-image:
    name: Development image
    timeout-minutes: 35
    needs: paths-filter
    if: needs.paths-filter.outputs.pass == 'true'
    permissions:
      packages: write
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: ${{ vars.AMD_ONLY == '1' && 'amd64' || 'arm64' }}
          - arch: ${{ vars.ARM_ONLY == '1' && 'arm64' || 'amd64' }}
    runs-on: ${{ matrix.arch == 'amd64' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    steps:
      - uses: actions/checkout@v4
      - name: Build ubuntu ansible
        id: build_ubuntu_ansible
        uses: ./.github/actions/docker/build-push-action
        with:
          context: docker/ansible
          file: docker/ansible/Dockerfile
          image: ${{ env.REGISTRY }}/dr-qp/ubuntu-ansible
          arch: ${{ matrix.arch }}
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build development docker image
        id: build
        uses: ./.github/actions/docker/build-push-action
        with:
          context: docker/ros
          file: docker/ros/desktop/ros-desktop.dockerfile
          image: ${{ env.REGISTRY }}/dr-qp/${{ env.ROS_DISTRO }}-ros-desktop
          arch: ${{ matrix.arch }}
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}
          build-args: |
            ROS_DISTRO=${{ env.ROS_DISTRO }}
            FROM_IMAGE=ghcr.io/dr-qp/ubuntu-ansible@${{ steps.build_ubuntu_ansible.outputs.digest }}

  merge-dev-image:
    name: Merge development image
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions:
      packages: write
    needs:
      - build-dev-image
    timeout-minutes: 35
    outputs:
      image: ${{ steps.merge.outputs.image }}
    steps:
      - uses: actions/checkout@v4

      - name: Merge and push ansible docker image
        uses: ./.github/actions/docker/multiarch-merge
        with:
          image: ${{ env.REGISTRY }}/dr-qp/ubuntu-ansible
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Merge and push development docker image
        id: merge
        uses: ./.github/actions/docker/multiarch-merge
        with:
          image: ${{ env.REGISTRY }}/dr-qp/${{ env.ROS_DISTRO }}-ros-desktop
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}

  ros-ci:
    name: Build and test ROS2 workspace
    needs: [merge-dev-image]
    timeout-minutes: 35
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: ${{ vars.AMD_ONLY == '1' && 'amd64' || 'arm64' }}
          - arch: ${{ vars.ARM_ONLY == '1' && 'arm64' || 'amd64' }}
    runs-on: ${{ matrix.arch == 'amd64' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions:
      packages: read
    container:
      image: ${{ needs.merge-dev-image.outputs.image }}
      options: --user rosdev # Mandatory! Sets user:group matching the one of the runner, allowing access to mounted paths
    steps:
      - name: copy .colcon to GHA's $HOME
        shell: bash
        run: |
          cp -r /home/<USER>/.colcon "$HOME"
          colcon mixin list

      - uses: actions/checkout@v4

      - name: Build and test ROS workspace
        shell: bash
        run: |
          set -x
          set -e

          source /opt/ros/${{ env.ROS_DISTRO }}/setup.bash
          time sudo apt update
          time rosdep update
          time ./scripts/ros-dep.sh
          time colcon build --mixin coverage-pytest ninja rel-with-deb-info --event-handlers=console_cohesion+ --cmake-args -D DRQP_ENABLE_COVERAGE=ON
          time colcon test --mixin coverage-pytest --event-handlers=console_cohesion+

      - name: Process llvm coverage
        shell: bash
        run: ./packages/cmake/llvm-cov-export-all.py ./build

      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v5
        if: ${{ env.ACT == '' && !cancelled() }}
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: unittests-${{ matrix.arch }}
          name: codecov-umbrella
          directory: build/

      - name: Test Summary
        uses: test-summary/action@v2
        if: ${{ env.ACT == '' && !cancelled() }}
        with:
          paths: "build/*/test_results/*/*.xunit.xml"

      - name: Discover test reports
        if: ${{ !cancelled() }}
        run: |
          set -x
          test_reports=$(find build -name "*.xunit.xml" | tr '\n' ',')
          test_reports=${test_reports%,}  # Remove trailing comma

          echo "result=$test_reports" >> "$GITHUB_OUTPUT"
          echo "$test_reports"
        shell: bash
        id: test_reports

      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ${{ steps.test_reports.outputs.result }}

      - uses: actions/upload-artifact@v4
        # upload the logs even when the build fails
        if: always()
        with:
          name: colcon-logs-${{ matrix.arch }}
          path: log
          retention-days: 90

  dev-container-ci:
      name: Devcontainer image
      needs: [merge-dev-image]
      timeout-minutes: 35
      strategy:
        fail-fast: false
        matrix:
          include:
            - arch: ${{ vars.AMD_ONLY == '1' && 'amd64' || 'arm64' }}
            - arch: ${{ vars.ARM_ONLY == '1' && 'arm64' || 'amd64' }}
      runs-on: ${{ matrix.arch == 'amd64' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
      permissions:
        packages: read
      steps:
          # Running container will fail with act due to no Docker in Docker, only Docker outside of Docker
          # https://github.com/Dr-QP/Dr.QP/wiki/Issue-using-%60act%60-and-%60devcontainers-ci%60
          # DinD requires an image that has dockerd installed, so run with -P ubuntu-latest=ghcr.io/corvina-r-d/ubuntu:act-22.04
          # DinD requires --priviliged option as well
          #
          #
          # DinD fails under QEMU emulation of arm64 on the amd64 CPUs:
          # iptables: Failed to initialize nft: Protocol not supported
          # https://github.com/multiarch/qemu-user-static/issues/191
        - name: optionally start DinD for act (local testing)
          if: env.ACT
          run: |
            dockerd -H unix:///var/run/dind.sock & sleep 5;
            echo "DOCKER_HOST=unix:///var/run/dind.sock" >> "${GITHUB_ENV}"

        - name: optionally test DinD for act (local testing)
          if: env.ACT
          run: |
            docker run --rm ghcr.io/dr-qp/hello-world:linux
            id

        - name: Install updated Skopeo needed for docker/buildx
          run: |
            sudo apt-get update
            sudo apt-get install skopeo -y

        - uses: actions/checkout@v4
        - uses: docker/setup-buildx-action@v3
        - uses: docker/login-action@v3
          with:
            registry: ${{ env.REGISTRY }}
            username: ${{ env.REGISTRY_USERNAME }}
            password: ${{ secrets.GITHUB_TOKEN }}

        - name: Patch devcontainer.json to use image built in this workflow
          run: |
            sed -i "s|ghcr.io/dr-qp/${{ env.ROS_DISTRO }}-ros-desktop:edge|${{ needs.merge-dev-image.outputs.image }}|g" .devcontainer/devcontainer.json

        - name: Build and test dev container image
          uses: devcontainers/ci@v0.3
          with:
            configFile: .devcontainer/devcontainer.json
            noCache: true
            runCmd: |
              set -x
              set -e
              id
              time sudo apt update
              time rosdep update
              time ./scripts/ros-dep.sh
              time colcon build --mixin ninja rel-with-deb-info --event-handlers=console_cohesion+
              time colcon test --event-handlers=console_cohesion+

  build-deploy-image:
    name: Deployment image
    needs: [merge-dev-image, ros-ci]
    timeout-minutes: 35
    strategy:
      fail-fast: false
      matrix:
        include:
          - arch: ${{ vars.AMD_ONLY == '1' && 'amd64' || 'arm64' }}
          - arch: ${{ vars.ARM_ONLY == '1' && 'arm64' || 'amd64' }}
    runs-on: ${{ matrix.arch == 'amd64' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions:
      packages: write
    steps:
      - uses: actions/checkout@v4

      - name: Build deployment docker image
        uses: ./.github/actions/docker/build-push-action
        with:
          context: docker/ros/deploy
          file: docker/ros/deploy/ros-deploy.dockerfile
          image: ${{ env.REGISTRY }}/dr-qp/${{ env.ROS_DISTRO }}-ros-deploy
          arch: ${{ matrix.arch }}
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}
          build-args: |
            ROS_DISTRO=${{ env.ROS_DISTRO }}
            GIT_SHA=${{ github.event.pull_request && github.event.pull_request.head.sha || github.sha }}
            BUILD_IMAGE=${{ needs.merge-dev-image.outputs.image }}

  merge-deploy-image:
    name: Merge deployment image
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions:
      packages: write
    needs:
      - build-deploy-image
    timeout-minutes: 35
    outputs:
      image: ${{ steps.merge.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      - name: Merge and push deployment docker image
        id: merge
        uses: ./.github/actions/docker/multiarch-merge
        with:
          image: ${{ env.REGISTRY }}/dr-qp/${{ env.ROS_DISTRO }}-ros-deploy
          registry: ${{ env.REGISTRY }}
          registry-username: ${{ env.REGISTRY_USERNAME }}
          registry-token: ${{ secrets.GITHUB_TOKEN }}
  merge-all:
    name: CI/CD finished
    needs: [build-dev-image, merge-dev-image, ros-ci, build-deploy-image, merge-deploy-image, dev-container-ci]
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions: {}
    if: ${{ success() || cancelled() || contains(needs.*.result, 'cancelled') || contains(needs.*.result, 'failure') }}
    steps:
      - name: All jobs succeeded!
        if: ${{ success() }}
        run: |
          exit 0
      - name: Some jobs have failed!
        if: ${{ contains(needs.*.result, 'failure') }}
        run: |
          exit 1
      - name: Some jobs have been cancelled!
        if: ${{ cancelled() || contains(needs.*.result, 'cancelled') }}
        run: |
          exit 1
