# For most projects, this workflow file will not need changing; you simply need
# to commit it to your repository.
#
# You may wish to alter this file to override the set of languages analyzed,
# or to provide custom queries or build logic.
#
# ******** NOTE ********
# We have attempted to detect the languages in your repository. Please check
# the `language` matrix defined below to confirm you have the correct set of
# supported CodeQL languages.
#
name: "CodeQL Advanced"

on:
  workflow_dispatch:
  push:
    branches:
      - main
    tags:
      - v*
  pull_request:
    branches:
      - main
  schedule:
    # Schedule for codeql is needed in case new vulnerabilities are found in dependencies
    # or new rules are added to CodeQL both of which happens outside of this repository.
    - cron: '32 17 * * 5' # “At 17:32 on Friday.” https://crontab.guru/#32_17_*_*_5
  merge_group:
    types:
      - checks_requested

concurrency:
  group: ${{ github.workflow }}-${{ github.ref || github.run_id }}
  cancel-in-progress: true

env:
  ROS_DISTRO: jazzy # duplicated in job.container.image
  CMAKE_EXPORT_COMPILE_COMMANDS: "1"
  REGISTRY_USERNAME: ${{ github.actor }} # for nektos/act, use your own username via --actor option

jobs:
  paths-filter:
    name: Need to run?
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions: {}
    outputs:
      pass: ${{ steps.filter.outputs.pass }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Filters
        uses: ./.github/actions/paths-filter
        id: filter
        with:
          extra-filter: .github/workflows/codeql.yml

  analyze:
    name: Analyze (${{ matrix.language }})
    needs: paths-filter
    if: needs.paths-filter.outputs.pass == 'true'
    # Runner size impacts CodeQL analysis time. To learn more, please see:
    #   - https://gh.io/recommended-hardware-resources-for-running-codeql
    #   - https://gh.io/supported-runners-and-hardware-resources
    #   - https://gh.io/using-larger-runners (GitHub.com only)
    # Consider using larger runners or machines with greater resources for possible analysis time improvements.
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/dr-qp/jazzy-ros-desktop:edge
      options: --user rosdev # Mandatory! Sets user:group matching the one of the runner, allowing access to mounted paths
    permissions:
      # required for all workflows
      security-events: write

      # required to fetch internal or private CodeQL packs
      packages: read

    strategy:
      fail-fast: false
      matrix:
        include:
        - language: c-cpp
          build-mode: manual
        - language: python
          build-mode: none
        - language: actions
          build-mode: none
          supports-dismissal: false
        - language: javascript-typescript
          build-mode: none
        # CodeQL supports the following values keywords for 'language': 'c-cpp', 'csharp', 'go', 'java-kotlin', 'javascript-typescript', 'python', 'ruby', 'swift'
        # Use `c-cpp` to analyze code written in C, C++ or both
        # Use 'java-kotlin' to analyze code written in Java, Kotlin or both
        # Use 'javascript-typescript' to analyze code written in JavaScript, TypeScript or both
        # To learn more about changing the languages that are analyzed or customizing the build mode for your analysis,
        # see https://docs.github.com/en/code-security/code-scanning/creating-an-advanced-setup-for-code-scanning/customizing-your-advanced-setup-for-code-scanning.
        # If you are analyzing a compiled language, you can modify the 'build-mode' for that language to customize how
        # your codebase is analyzed, see https://docs.github.com/en/code-security/code-scanning/creating-an-advanced-setup-for-code-scanning/codeql-code-scanning-for-compiled-languages
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    # Add any setup steps before running the `github/codeql-action/init` action.
    # This includes steps like installing compilers or runtimes (`actions/setup-node`
    # or others). This is typically only required for manual builds.
    # - name: Setup runtime (example)
    #   uses: actions/setup-example@v1

    # Initializes the CodeQL tools for scanning.
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        build-mode: ${{ matrix.build-mode }}
        # If you wish to specify custom queries, you can do so here or in a config file.
        # By default, queries listed here will override any specified in a config file.
        # Prefix the list here with "+" to use these queries and those in the config file.

        # For more details on CodeQL's query packs, refer to: https://docs.github.com/en/code-security/code-scanning/automatically-scanning-your-code-for-vulnerabilities-and-errors/configuring-code-scanning#using-queries-in-ql-packs
        # queries: security-extended,security-and-quality
        config-file: ./.github/codeql/codeql-config.yml

    # If the analyze step fails for one of the languages you are analyzing with
    # "We were unable to automatically build your code", modify the matrix above
    # to set the build mode to "manual" for that language. Then modify this step
    # to build your code.
    # ℹ️ Command-line programs to run using the OS shell.
    # 📚 See https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions#jobsjob_idstepsrun
    - name: Build C++ code
      if: matrix.build-mode == 'manual'
      shell: bash
      run: |
        sudo apt-get update
        source "/opt/ros/$ROS_DISTRO/setup.bash"
        rosdep update
        ./scripts/ros-dep.sh
        colcon build --cmake-args -GNinja -DCMAKE_BUILD_TYPE=RelWithDebInfo

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"
        output: sarif-results
        upload: failure-only

    - name: Find sarif file
      id: sarif
      run: |
        file=$(find sarif-results -name "*.sarif")
        echo "file=$PWD/$file" >> "$GITHUB_OUTPUT"

    - name: Upload loc as a Build Artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{matrix.language}}-raw.sarif
        path: ${{ steps.sarif.outputs.file }}
        retention-days: 7

    - name: filter-sarif
      uses: advanced-security/filter-sarif@v1
      with:
        input: ${{ steps.sarif.outputs.file }}
        output: ${{ steps.sarif.outputs.file }}
        patterns: |
          -**/build/**
          -**/install/**
          -**/logs/**
          -**/examples/**
          -**/test/**
          -**/node_modules/**

    - name: Upload SARIF
      uses: github/codeql-action/upload-sarif@v3
      id: upload-sarif
      with:
        sarif_file: ${{ steps.sarif.outputs.file }}

      # Unlock inline mechanism to suppress CodeQL warnings.
      # https://github.com/github/codeql/issues/11427#issuecomment-1721059096
    - name: Dismiss alerts that were suppressed with inline comments
      # run auto dismissal only on main branch, aka reviewed code only, since dismissals are global
      if: ${{ github.ref == 'refs/heads/main' }}
      uses: advanced-security/dismiss-alerts@v1
      with:
        sarif-id: ${{ steps.upload-sarif.outputs.sarif-id }}
        sarif-file: ${{ steps.sarif.outputs.file }}
      env:
        GITHUB_TOKEN: ${{ github.token }}

    - name: Upload loc as a Build Artifact
      uses: actions/upload-artifact@v4
      with:
        name: ${{matrix.language}}-filtered.sarif
        path: ${{ steps.sarif.outputs.file }}
        retention-days: 7

  merge-all:
    name: Code Analysis finished
    needs: [analyze]
    runs-on: ${{ vars.AMD_ONLY == '1' && 'ubuntu-24.04' || 'ubuntu-24.04-arm' }}
    permissions: {}
    if: ${{ success() || cancelled() || contains(needs.*.result, 'cancelled') || contains(needs.*.result, 'failure') }}
    steps:
      - name: All jobs succeeded!
        if: ${{ success() }}
        run: |
          exit 0
      - name: Some jobs have failed!
        if: ${{ contains(needs.*.result, 'failure') }}
        run: |
          exit 1
      - name: Some jobs have been cancelled!
        if: ${{ cancelled() || contains(needs.*.result, 'cancelled') }}
        run: |
          exit 1
