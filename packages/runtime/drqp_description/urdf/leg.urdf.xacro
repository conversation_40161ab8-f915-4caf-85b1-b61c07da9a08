<?xml version="1.0"?>
<robot xmlns:xacro="http://ros.org/wiki/xacro">

  <xacro:include filename="$(find drqp_description)/urdf/inertial_macros.xacro"/>
  <xacro:macro name="inertial_stub" params="">
    <xacro:property name="tiny_value" value="0.01"/>
    <xacro:inertial_box mass="${tiny_value}" x="${tiny_value}" y="${tiny_value}" z="${tiny_value}">
      <origin xyz="0 0 0" rpy="0 0 0"/>
    </xacro:inertial_box>
  </xacro:macro>

  <xacro:macro name="leg" params="robot_name prefix x y yaw">

  <joint name="${robot_name}/${prefix}_coxa_servo" type="fixed">
    <origin rpy="0 0 ${yaw}" xyz="${x} ${y} 0"/>
    <parent link="${robot_name}/base_center_link"/>
    <child link="${robot_name}/${prefix}_coxa_servo"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_coxa_servo'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <link name="${robot_name}/${prefix}_coxa_servo">
    <visual>
      <origin xyz="0 0 0" rpy="0 0 ${-pi/2}" />
      <geometry>
        <mesh filename="package://drqp_description/meshes/servo.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 ${-pi/2}" />
      <geometry>
        <mesh filename="package://drqp_description/meshes/servo-collision.stl" />
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.019066 -0.000002 0.006577"/>
      <mass value="0.02471" />
      <inertia ixx="0.0000215400" iyy="0.00002657000" izz="0.00002925000" ixy="-0.00000000122" ixz="-0.00000192741" iyz="-0.00000000001" />
    </inertial>
  </link>


  <joint name="${robot_name}/${prefix}_coxa" type="revolute">
    <axis xyz="0 0 1"/>
    <limit effort="3" lower="${radians(-90)}" upper="${radians(90)}" velocity="${pi}"/>
    <parent link="${robot_name}/${prefix}_coxa_servo"/>
    <child link="${robot_name}/${prefix}_coxa_f2_link_1"/>
    <!-- <dynamics damping="100" friction="0.1" /> -->
  </joint>
  <gazebo reference="${robot_name}/${prefix}_coxa">
    <dampingFactor>0.04</dampingFactor>
    <implicitSpringDamper>true</implicitSpringDamper>
    <!-- <springStiffness>2</springStiffness> -->
    <springReference>0.01</springReference>
  </gazebo>

  <link name="${robot_name}/${prefix}_coxa_f2_link_1">
    <visual>
      <origin rpy="0 0 ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F2.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F2-collision.stl" />
      </geometry>
    </collision>
    <xacro:inertial_stub />
  </link>
  <joint name="${robot_name}/${prefix}_coxa_inner" type="fixed">
    <origin rpy="0 0 0" xyz="${0.0265 * 2} 0 0"/>
    <parent link="${robot_name}/${prefix}_coxa_f2_link_1"/>
    <child link="${robot_name}/${prefix}_coxa_f2_link_2"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_coxa_inner'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>


  <link name="${robot_name}/${prefix}_coxa_f2_link_2">
    <visual>
      <origin rpy="0 ${pi/2} ${-pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F2.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 ${pi/2} ${-pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F2-collision.stl" />
      </geometry>
    </collision>
    <xacro:inertial_stub />
  </link>

  <joint name="${robot_name}/${prefix}_femur" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="3" lower="${radians(-98)}" upper="${radians(90)}" velocity="${pi}"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="${robot_name}/${prefix}_coxa_f2_link_2"/>
    <child link="${robot_name}/${prefix}_femur_servo"/>
    <!-- <dynamics damping="100" friction="0.1" /> -->
  </joint>
  <gazebo reference="${robot_name}/${prefix}_femur">
    <dampingFactor>0.04</dampingFactor>
    <implicitSpringDamper>true</implicitSpringDamper>
    <!-- <springStiffness>2</springStiffness> -->
    <springReference>0.01</springReference>
  </gazebo>

  <link name="${robot_name}/${prefix}_femur_servo">
    <visual>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0" />
      <geometry>
        <!--
          Origin is at the actuator center
          36mm - distance from actuator center to the center of the holes on the back
         -->
        <mesh filename="package://drqp_description/meshes/servo.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://drqp_description/meshes/servo-collision.stl" />
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.019066 -0.000002 0.006577"/>
      <mass value="0.02471" />
      <inertia ixx="0.0000215400" iyy="0.00002657000" izz="0.00002925000" ixy="-0.00000000122" ixz="-0.00000192741" iyz="-0.00000000001" />
    </inertial>
  </link>

  <joint name="${robot_name}/${prefix}_femur_inner_1" type="fixed">
    <origin rpy="0 0 0" xyz="${0.036 + 0.0055} 0 0"/>
    <parent link="${robot_name}/${prefix}_femur_servo"/>
    <child link="${robot_name}/${prefix}_femur_link_f3"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_femur_inner_1'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <link name="${robot_name}/${prefix}_femur_link_f3">
    <visual>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <!--
          Origin is at the center of the flat plane
          5.5mm - distance from origin to center of the side mounting holes
         -->
        <mesh filename="package://drqp_description/meshes/F3.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F3-collision.stl" />
      </geometry>
    </collision>
    <xacro:inertial_stub />
  </link>

  <joint name="${robot_name}/${prefix}_femur_inner_2" type="fixed">
    <origin rpy="0 0 0" xyz="0.023 0 -0.015"/>
    <parent link="${robot_name}/${prefix}_femur_link_f3"/>
    <child link="${robot_name}/${prefix}_femur_link_f1"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_femur_inner_2'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <link name="${robot_name}/${prefix}_femur_link_f1">
    <visual>
      <origin rpy="${pi} ${-pi/2} ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <!--
          Origin is at the servo mount/pivot
          15.0mm - distance from origin to the center mount holes on the mount plane - x axis
          23.0mm - distance from origin to mount plane - y axis
         -->
        <mesh filename="package://drqp_description/meshes/F1.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="${pi} ${-pi/2} ${pi/2}" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/F1-collision.stl" />
      </geometry>
    </collision>
    <xacro:inertial_stub />
  </link>

  <joint name="${robot_name}/${prefix}_tibia" type="revolute">
    <axis xyz="0 1 0"/>
    <limit effort="3" lower="${radians(-80)}" upper="${radians(110)}" velocity="${pi}"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
    <parent link="${robot_name}/${prefix}_femur_link_f1"/>
    <child link="${robot_name}/${prefix}_tibia_servo"/>
    <!-- <dynamics damping="100" friction="0.1" /> -->
  </joint>
  <gazebo reference="${robot_name}/${prefix}_tibia">
    <dampingFactor>0.04</dampingFactor>
    <implicitSpringDamper>true</implicitSpringDamper>
    <!-- <springStiffness>2</springStiffness> -->
    <springReference>0.01</springReference>
  </gazebo>

  <link name="${robot_name}/${prefix}_tibia_servo">
    <visual>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://drqp_description/meshes/servo.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 ${pi/2} ${pi/2}" xyz="0 0 0" />
      <geometry>
        <mesh filename="package://drqp_description/meshes/servo-collision.stl" />
      </geometry>
    </collision>
    <inertial>
      <origin rpy="0 0 0" xyz="0.036797 0.000054 -0.011991"/>
      <mass value="0.171825" />
      <inertia ixx="0.0001762000" iyy="0.0005158000" izz="0.0003768000" ixy="-0.0000014522" ixz="0.0001789000" iyz="0.0000006181" />
    </inertial>
  </link>

  <joint name="${robot_name}/${prefix}_tibia_leg" type="fixed">
    <origin rpy="0 0 0" xyz="0.0325 0 0"/>
    <parent link="${robot_name}/${prefix}_tibia_servo"/>
    <child link="${robot_name}/${prefix}_tibia_leg"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_tibia_leg'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>

  <link name="${robot_name}/${prefix}_tibia_leg">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <!--
          Origin is at end servo attachment, 32.5 between servo pivot and this point
         -->
        <mesh filename="package://drqp_description/meshes/Tibia.stl" />
      </geometry>
      <material name="drqp_black"/>
    </visual>
    <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="package://drqp_description/meshes/Tibia-collision.stl" />
      </geometry>
    </collision>
    <xacro:inertial_stub />
  </link>

  <joint name="${robot_name}/${prefix}_foot" type="fixed">
    <origin rpy="0 0 0" xyz="0.052797 0 -0.088395"/>
    <parent link="${robot_name}/${prefix}_tibia_leg"/>
    <child link="${robot_name}/${prefix}_foot_link"/>
  </joint>
  <gazebo reference='${robot_name}/${prefix}_foot'>
    <preserveFixedJoint>true</preserveFixedJoint>
  </gazebo>


  <link name="${robot_name}/${prefix}_foot_link"/>

</xacro:macro>
</robot>
